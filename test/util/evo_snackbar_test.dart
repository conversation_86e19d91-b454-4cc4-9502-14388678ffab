import 'package:evoapp/feature/feature_toggle.dart';
import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/util/functions.dart';
import 'package:evoapp/widget/custom_snackbar_widget.dart';
import 'package:evoapp/widget/custom_snackbar_widget_v2.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/global_key_provider.dart';
import 'package:flutter_common_package/util/utils.dart';
import 'package:flutter_common_package/widget/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'flutter_test_config.dart';

class TestSnackBarWrapper extends SnackBarWrapper {
  bool hasCallCancelSnackBar = false;
  double fakeMarginBottomRatio = 20.0;

  TestSnackBarWrapper(super.featureToggle);

  @override
  void cancelSnackBar() {
    hasCallCancelSnackBar = true;
  }

  @override
  double? getMarginBottom({double? marginBottomRatio, FlutterView? view}) {
    return fakeMarginBottomRatio;
  }
}

void main() {
  late CommonImageProvider commonImageProvider;

  late TestSnackBarWrapper testSnackBarWrapper;
  late EvoSnackBar testEvoSnackBar;
  late FeatureToggle featureToggle;

  setUpAll(() {
    TestWidgetsFlutterBinding.ensureInitialized();
    registerFallbackValue(SnackBarType.success);

    getItRegisterFeatureToggle();

    getIt.registerSingleton<GlobalKeyProvider>(GlobalKeyProvider());

    getItRegisterColor();
    getIt.registerLazySingleton<CommonUtilFunction>(() => CommonUtilFunction());
    getIt.registerLazySingleton<EvoUtilFunction>(() => MockEvoUtilFunction());
    getIt.registerLazySingleton<CommonImageProvider>(() => MockEvoImageProvider());
    getItRegisterTextStyle();

    commonImageProvider = getIt.get<CommonImageProvider>();
    featureToggle = getIt.get<FeatureToggle>();

    when(() => commonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
          cornerRadius: any(named: 'cornerRadius'),
          cacheWidth: any(named: 'cacheWidth'),
          cacheHeight: any(named: 'cacheHeight'),
          package: any(named: 'package'),
        )).thenAnswer((_) => Container());
  });

  setUp(() {
    testSnackBarWrapper = TestSnackBarWrapper(featureToggle);
    testEvoSnackBar = EvoSnackBar(testSnackBarWrapper);

    when(() => featureToggle.enableRevampUiFeature).thenReturn(false);
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('SnackBarType', () {
    test('should have the correct number of enum values', () {
      const List<SnackBarType> values = SnackBarType.values;
      expect(values.length, 4);
    });

    test('should have a specific order of enum values', () {
      const List<SnackBarType> values = SnackBarType.values;
      expect(values, <SnackBarType>[
        SnackBarType.success,
        SnackBarType.error,
        SnackBarType.neutral,
        SnackBarType.warning,
      ]);
    });
  });

  group('SnackBarDuration', () {
    test('should have the correct number of enum values', () {
      const List<SnackBarDuration> values = SnackBarDuration.values;
      expect(values.length, 3);
    });

    test('should have a specific order of enum values', () {
      const List<SnackBarDuration> values = SnackBarDuration.values;
      expect(values, <SnackBarDuration>[
        SnackBarDuration.short,
        SnackBarDuration.medium,
        SnackBarDuration.long,
      ]);
    });

    test('should have the correct duration values', () {
      expect(SnackBarDuration.short.value, 3);
      expect(SnackBarDuration.medium.value, 5);
      expect(SnackBarDuration.long.value, 10);
    });
  });

  Future<bool?> showSnackBarForTesting({
    required String message,
    required SnackBarType typeSnackBar,
    required String description,
    int? durationInSec,
  }) async {
    return await testEvoSnackBar.show(
      message,
      typeSnackBar: typeSnackBar,
      durationInSec: durationInSec ?? SnackBarDuration.short.value,
      marginBottomRatio: SnackBarWrapper.defaultMarginBottomRatio,
      description: description,
    );
  }

  void verifyParamSnackBar(
    WidgetTester tester, {
    int? durationInSec,
    bool? isRevamp,
  }) {
    final Finder finderSnackBar = find.byType(SnackBar);
    expect(finderSnackBar, findsOneWidget);
    final SnackBar snackBarWidget = tester.widget(finderSnackBar);
    expect(snackBarWidget.backgroundColor, Colors.transparent);
    expect(snackBarWidget.behavior, SnackBarBehavior.floating);
    expect(snackBarWidget.dismissDirection, DismissDirection.none);
    expect(snackBarWidget.padding, const EdgeInsets.symmetric(horizontal: 20, vertical: 12));
    expect(snackBarWidget.margin, const EdgeInsets.only(bottom: 20.0));
    expect(snackBarWidget.content,
        isRevamp == true ? isA<CustomSnackBarWidgetV2>() : isA<CustomSnackBarWidget>());
    expect(
        snackBarWidget.duration, Duration(seconds: durationInSec ?? SnackBarDuration.long.value));
  }

  void verifyParamCustomSnackBar(
    WidgetTester tester, {
    required String message,
    required Color background,
    required Color borderColor,
    required String leadingIconName,
    String? description,
  }) {
    final Finder finderCustomSnackBar = find.byType(CustomSnackBarWidget);
    expect(finderCustomSnackBar, findsOneWidget);
    expect(find.text(message), findsOneWidget);
    final CustomSnackBarWidget customSnackBarWidget = tester.widget(finderCustomSnackBar);
    expect(customSnackBarWidget.text, message);
    expect(customSnackBarWidget.background, background);
    expect(customSnackBarWidget.borderColor, borderColor);

    verify(() => commonImageProvider.asset(
          leadingIconName,
          width: 20,
          height: 20,
        )).called(1);

    expect(customSnackBarWidget.description, description);

    customSnackBarWidget.onClose?.call();
    expect(testSnackBarWrapper.hasCallCancelSnackBar, true);
  }

  group('verify EvoSnackBar', () {
    test('verify defaultMarginBottomRatio', () {
      expect(SnackBarWrapper.defaultMarginBottomRatio, 0.08);
    });

    test('verify setEnable', () {
      expect(testEvoSnackBar.enable, true);

      testEvoSnackBar.setEnable = false;
      expect(testEvoSnackBar.enable, false);

      testEvoSnackBar.setEnable = true;
      expect(testEvoSnackBar.enable, true);
    });

    testWidgets('verify show() with type = error', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_error';
      const String fakeDescriptionSnackBar = 'fake_description_error';

      await tester.pumpWidget(MaterialApp(
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  showSnackBarForTesting(
                    message: fakeMessageSnackBar,
                    typeSnackBar: SnackBarType.error,
                    description: fakeDescriptionSnackBar,
                  );
                },
                child: const Text('Show SnackBar'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('Show SnackBar'));
      await tester.pump();

      // Verify SnackBar
      verifyParamSnackBar(tester, durationInSec: SnackBarDuration.short.value);

      // Verify CustomSnackBarWidget
      verifyParamCustomSnackBar(
        tester,
        message: fakeMessageSnackBar,
        background: const Color(0xFFFFE5DC),
        borderColor: const Color(0xFFFFC6B2),
        leadingIconName: EvoImages.icToastError,
        description: fakeDescriptionSnackBar,
      );
    });

    testWidgets('verify show() with type = success', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_success';
      const String fakeDescriptionSnackBar = 'fake_description_success';

      await tester.pumpWidget(MaterialApp(
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  showSnackBarForTesting(
                    message: fakeMessageSnackBar,
                    typeSnackBar: SnackBarType.success,
                    description: fakeDescriptionSnackBar,
                  );
                },
                child: const Text('Show SnackBar'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('Show SnackBar'));
      await tester.pump();

      // Verify SnackBar
      verifyParamSnackBar(tester, durationInSec: SnackBarDuration.short.value);

      // Verify CustomSnackBarWidget
      verifyParamCustomSnackBar(
        tester,
        message: fakeMessageSnackBar,
        background: const Color(0xFFECF9F3),
        borderColor: const Color(0xFFD7F4E5),
        leadingIconName: EvoImages.icCircleCheck,
        description: fakeDescriptionSnackBar,
      );
    });

    testWidgets('verify show() with type = warning', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_warning';
      const String fakeDescriptionSnackBar = 'fake_description_warning';

      await tester.pumpWidget(MaterialApp(
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  showSnackBarForTesting(
                    message: fakeMessageSnackBar,
                    typeSnackBar: SnackBarType.warning,
                    description: fakeDescriptionSnackBar,
                  );
                },
                child: const Text('Show SnackBar'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('Show SnackBar'));
      await tester.pump();

      // Verify SnackBar
      verifyParamSnackBar(tester, durationInSec: SnackBarDuration.short.value);

      // Verify CustomSnackBarWidget
      verifyParamCustomSnackBar(
        tester,
        message: fakeMessageSnackBar,
        background: const Color(0xFFFEF3DC),
        borderColor: const Color(0xFFFEE7B9),
        leadingIconName: EvoImages.icSnackBarWarning,
        description: fakeDescriptionSnackBar,
      );
    });

    testWidgets('verify show() with type = neutral', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_neutral';
      const String fakeDescriptionSnackBar = 'fake_description_neutral';

      await tester.pumpWidget(MaterialApp(
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  showSnackBarForTesting(
                    message: fakeMessageSnackBar,
                    typeSnackBar: SnackBarType.neutral,
                    description: fakeDescriptionSnackBar,
                  );
                },
                child: const Text('Show SnackBar'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('Show SnackBar'));
      await tester.pump();

      // Verify SnackBar
      verifyParamSnackBar(tester, durationInSec: SnackBarDuration.short.value);

      // Verify CustomSnackBarWidget
      verifyParamCustomSnackBar(
        tester,
        message: fakeMessageSnackBar,
        background: const Color(0xFFEBF3FF),
        borderColor: const Color(0xFFBFDBFE),
        leadingIconName: EvoImages.icSnackBarNeutral,
        description: fakeDescriptionSnackBar,
      );
    });

    testWidgets('verify show() with duplicate message', (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_success';
      const String fakeDescriptionSnackBar = 'fake_description_success';
      final int durationInSec = SnackBarDuration.short.value;

      await tester.runAsync(() async {
        bool? valueShowSnackBar = false;
        await tester.pumpWidget(MaterialApp(
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return GestureDetector(
                  onTap: () async {
                    valueShowSnackBar = await showSnackBarForTesting(
                      message: fakeMessageSnackBar,
                      typeSnackBar: SnackBarType.success,
                      description: fakeDescriptionSnackBar,
                      durationInSec: durationInSec,
                    );
                  },
                  child: const Text('Show SnackBar'),
                );
              },
            ),
          ),
        ));

        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, true);
        final Finder finderSnackBar = find.byType(SnackBar);
        expect(finderSnackBar, findsOneWidget);
        final SnackBar snackBarWidget = tester.widget(finderSnackBar);
        expect(snackBarWidget.duration, Duration(seconds: durationInSec));

        // Delay for checking duplicate message
        await Future<void>.delayed(const Duration(seconds: 1));

        // continue to tap
        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, false);

        // Delay for checking duplicate message
        await Future<void>.delayed(Duration(seconds: durationInSec));

        // continue to tap
        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, true);
      });
    });

    testWidgets('verify show() after the user pressed close button on SnackBar',
        (WidgetTester tester) async {
      const String fakeMessageSnackBar = 'fake_message_success';
      const String fakeDescriptionSnackBar = 'fake_description_success';
      final int durationInSec = SnackBarDuration.short.value;

      await tester.runAsync(() async {
        bool? valueShowSnackBar = false;
        await tester.pumpWidget(MaterialApp(
          scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
          home: Scaffold(
            body: Builder(
              builder: (BuildContext context) {
                return GestureDetector(
                  onTap: () async {
                    valueShowSnackBar = await showSnackBarForTesting(
                      message: fakeMessageSnackBar,
                      typeSnackBar: SnackBarType.success,
                      description: fakeDescriptionSnackBar,
                      durationInSec: durationInSec,
                    );
                  },
                  child: const Text('Show SnackBar'),
                );
              },
            ),
          ),
        ));

        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, true);
        final Finder finderSnackBar = find.byType(SnackBar);
        expect(finderSnackBar, findsOneWidget);
        final SnackBar snackBarWidget = tester.widget(finderSnackBar);
        expect(snackBarWidget.duration, Duration(seconds: durationInSec));

        // verify close SnackBar
        final Finder finderCustomSnackBar = find.byType(CustomSnackBarWidget);
        expect(finderCustomSnackBar, findsOneWidget);
        final CustomSnackBarWidget customSnackBar = tester.widget(finderCustomSnackBar);
        customSnackBar.onClose?.call();

        await tester.tap(find.text('Show SnackBar'));
        await tester.pump();

        expect(valueShowSnackBar, true);
      });
    });

    testWidgets('verify show() with enable revamp ui feature', (WidgetTester tester) async {
      final bool isRevamp = true;
      when(() => featureToggle.enableRevampUiFeature).thenReturn(isRevamp);

      const String fakeMessageSnackBar = 'fake_message_error';
      const String fakeDescriptionSnackBar = 'fake_description_error';

      await tester.pumpWidget(MaterialApp(
        scaffoldMessengerKey: getIt.get<GlobalKeyProvider>().scaffoldMessengerKey,
        home: Scaffold(
          body: Builder(
            builder: (BuildContext context) {
              return GestureDetector(
                onTap: () {
                  showSnackBarForTesting(
                    message: fakeMessageSnackBar,
                    typeSnackBar: SnackBarType.error,
                    description: fakeDescriptionSnackBar,
                  );
                },
                child: const Text('Show SnackBar'),
              );
            },
          ),
        ),
      ));

      await tester.tap(find.text('Show SnackBar'));
      await tester.pump();

      // Verify SnackBar
      verifyParamSnackBar(
        tester,
        durationInSec: SnackBarDuration.short.value,
        isRevamp: isRevamp,
      );

      final CustomSnackBarWidgetV2 customSnackBarWidget =
          tester.widget(find.byType(CustomSnackBarWidgetV2));

      customSnackBarWidget.onClose?.call();
      expect(testSnackBarWrapper.hasCallCancelSnackBar, true);
    });
  });
}
