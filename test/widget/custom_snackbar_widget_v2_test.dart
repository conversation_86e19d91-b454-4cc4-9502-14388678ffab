import 'package:evoapp/prepare_for_app_initiation.dart';
import 'package:evoapp/resources/resources.dart';
import 'package:evoapp/util/evo_snackbar.dart';
import 'package:evoapp/widget/custom_snackbar_widget_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_common_package/widget/common_image_provider.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../util/flutter_test_config.dart';

class MockCommonImageProvider extends Mock implements CommonImageProvider {}

void main() {
  late CommonImageProvider mockCommonImageProvider;

  setUpAll(() {
    getItRegisterColor();
    getItRegisterTextStyle();
    getItRegisterMockCommonUtilFunctionAndImageProvider();

    mockCommonImageProvider = getIt.get<CommonImageProvider>();
    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
        )).thenAnswer((_) => Container());
  });

  setUp(() {
    reset(mockCommonImageProvider);
    when(() => mockCommonImageProvider.asset(
          any(),
          width: any(named: 'width'),
          height: any(named: 'height'),
          color: any(named: 'color'),
          fit: any(named: 'fit'),
        )).thenAnswer((_) => Container());
  });

  tearDownAll(() {
    getIt.reset();
  });

  group('CustomSnackBarWidgetV2', () {
    group('Constructor and initial values', () {
      test('should have correct initial values with required parameters only', () {
        const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
          text: 'Test message',
        );

        expect(widget.text, 'Test message');
        expect(widget.description, isNull);
        expect(widget.onClose, isNull);
        expect(widget.typeSnackBar, SnackBarType.success);
      });

      test('should have correct initial values with all parameters', () {
        void onCloseCallback() {}

        final CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
          text: 'Test message',
          description: 'Test description',
          onClose: onCloseCallback,
          typeSnackBar: SnackBarType.error,
        );

        expect(widget.text, 'Test message');
        expect(widget.description, 'Test description');
        expect(widget.onClose, onCloseCallback);
        expect(widget.typeSnackBar, SnackBarType.error);
      });
    });

    group('Widget rendering', () {
      testWidgets('should render with required text only', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Test message',
              ),
            ),
          ),
        );

        expect(find.text('Test message'), findsOneWidget);
        expect(find.byType(Container), findsWidgets);
        expect(find.byType(Row), findsOneWidget);
        expect(find.byType(InkWell), findsOneWidget);
      });

      testWidgets('should render with text and description', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Test message',
                description: 'Test description',
              ),
            ),
          ),
        );

        expect(find.text('Test message'), findsOneWidget);
        expect(find.text('Test description'), findsOneWidget);
      });

      testWidgets('should not render description when null', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Test message',
                description: null,
              ),
            ),
          ),
        );

        expect(find.text('Test message'), findsOneWidget);
        expect(find.byType(SizedBox), findsWidgets);
      });

      testWidgets('should call onClose when close button is tapped', (WidgetTester tester) async {
        bool onCloseCalled = false;
        void onCloseCallback() {
          onCloseCalled = true;
        }

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Test message',
                onClose: onCloseCallback,
              ),
            ),
          ),
        );

        await tester.tap(find.byType(InkWell));
        expect(onCloseCalled, isTrue);
      });

      testWidgets('should render close button with correct icon', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Test message',
              ),
            ),
          ),
        );

        verify(() => mockCommonImageProvider.asset(
              EvoImages.icCloseWhite,
              width: 20,
              height: 20,
            )).called(1);
      });

      testWidgets('should have correct container styling', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Test message',
                typeSnackBar: SnackBarType.success,
              ),
            ),
          ),
        );

        final Finder containerFinder = find.byType(Container).first;
        final Container container = tester.widget<Container>(containerFinder);

        expect(container.padding, const EdgeInsets.only(left: 12.0));

        final BoxDecoration decoration = container.decoration as BoxDecoration;
        expect(decoration.borderRadius, BorderRadius.circular(12.0));
        expect(decoration.color, evoColors.snackBarSuccessBackgroundV2);
      });
    });

    group('SnackBar types', () {
      testWidgets('should render success type correctly', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Success message',
                typeSnackBar: SnackBarType.success,
              ),
            ),
          ),
        );

        verify(() => mockCommonImageProvider.asset(
              EvoImages.icSnackBarSuccess,
              width: 20,
              height: 20,
            )).called(1);
      });

      testWidgets('should render error type correctly', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Error message',
                typeSnackBar: SnackBarType.error,
              ),
            ),
          ),
        );

        verify(() => mockCommonImageProvider.asset(
              EvoImages.icSnackBarError,
              width: 20,
              height: 20,
            )).called(1);
      });

      testWidgets('should render warning type correctly', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Warning message',
                typeSnackBar: SnackBarType.warning,
              ),
            ),
          ),
        );

        verify(() => mockCommonImageProvider.asset(
              EvoImages.icSnackBarWarningV2,
              width: 20,
              height: 20,
            )).called(1);
      });

      testWidgets('should render neutral type correctly', (WidgetTester tester) async {
        await tester.pumpWidget(
          const MaterialApp(
            home: Scaffold(
              body: CustomSnackBarWidgetV2(
                text: 'Neutral message',
                typeSnackBar: SnackBarType.neutral,
              ),
            ),
          ),
        );

        verify(() => mockCommonImageProvider.asset(
              EvoImages.icSnackBarInfo,
              width: 20,
              height: 20,
            )).called(1);
      });
    });

    group('@visibleForTesting methods', () {
      group('leadingIcon', () {
        test('should return correct icon for success type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.success,
          );

          final Widget leadingIcon = widget.leadingIcon;
          expect(leadingIcon, isA<Padding>());
        });

        test('should return correct icon for error type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.error,
          );

          final Widget leadingIcon = widget.leadingIcon;
          expect(leadingIcon, isA<Padding>());
        });

        test('should return correct icon for warning type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.warning,
          );

          final Widget leadingIcon = widget.leadingIcon;
          expect(leadingIcon, isA<Padding>());
        });

        test('should return correct icon for neutral type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.neutral,
          );

          final Widget leadingIcon = widget.leadingIcon;
          expect(leadingIcon, isA<Padding>());
        });

        test('should return SizedBox.shrink for null type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: null,
          );

          final Widget leadingIcon = widget.leadingIcon;
          expect(leadingIcon, isA<SizedBox>());
        });
      });

      group('background', () {
        test('should return correct background color for success type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.success,
          );

          final Color background = widget.background;
          expect(background, evoColors.snackBarSuccessBackgroundV2);
        });

        test('should return correct background color for error type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.error,
          );

          final Color background = widget.background;
          expect(background, evoColors.snackBarErrorBackgroundV2);
        });

        test('should return correct background color for warning type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.warning,
          );

          final Color background = widget.background;
          expect(background, evoColors.snackBarWarningBackgroundV2);
        });

        test('should return correct background color for neutral type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.neutral,
          );

          final Color background = widget.background;
          expect(background, evoColors.snackBarNeutralBackgroundV2);
        });

        test('should return default background color for null type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: null,
          );

          final Color background = widget.background;
          expect(background, evoColors.snackBarDefaultBackgroundV2);
        });
      });

      group('textColor', () {
        test('should return textActive color for warning type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.warning,
          );

          final Color textColor = widget.textColor;
          expect(textColor, evoColors.textActive);
        });

        test('should return white color for success type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.success,
          );

          final Color textColor = widget.textColor;
          expect(textColor, Colors.white);
        });

        test('should return white color for error type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.error,
          );

          final Color textColor = widget.textColor;
          expect(textColor, Colors.white);
        });

        test('should return white color for neutral type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: SnackBarType.neutral,
          );

          final Color textColor = widget.textColor;
          expect(textColor, Colors.white);
        });

        test('should return white color for null type', () {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test',
            typeSnackBar: null,
          );

          final Color textColor = widget.textColor;
          expect(textColor, Colors.white);
        });
      });

      group('itemTitleAndDescription', () {
        testWidgets('should render title and description when description is provided',
            (WidgetTester tester) async {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test title',
            description: 'Test description',
          );

          await tester.pumpWidget(
            MaterialApp(
              home: Scaffold(
                body: Row(
                  children: [widget.itemTitleAndDescription()],
                ),
              ),
            ),
          );

          expect(find.text('Test title'), findsOneWidget);
          expect(find.text('Test description'), findsOneWidget);
          expect(find.byType(Expanded), findsOneWidget);
          expect(find.byType(Column), findsOneWidget);
        });

        testWidgets('should render only title when description is null',
            (WidgetTester tester) async {
          const CustomSnackBarWidgetV2 widget = CustomSnackBarWidgetV2(
            text: 'Test title',
            description: null,
          );

          await tester.pumpWidget(
            MaterialApp(
              home: Scaffold(
                body: Row(
                  children: [widget.itemTitleAndDescription()],
                ),
              ),
            ),
          );

          expect(find.text('Test title'), findsOneWidget);
          expect(find.byType(SizedBox), findsOneWidget);
          expect(find.byType(Expanded), findsOneWidget);
          expect(find.byType(Column), findsOneWidget);
        });
      });
    });
  });
}
